# 🇿🇦 MzansiServices – A Community-Powered Service Directory & P2P Tracker

Welcome to **MzansiServices**, a beginner-friendly tech project built by four passionate learners from WeThinkCode_ JHB campus. Our mission is simple: **make it easier for people in townships and under-resourced areas to connect, offer services, and track their income – even without a fancy app or expensive tools**.

---

## 💡 What's the idea?

MzansiServices is a simple website where anyone can:

- 📢 **List a service** they offer (like braiding, tutoring, laptop repairs).
- 🔎 **View local services** available nearby.
- 📞 **Contact a service provider** (via WhatsApp or SMS).
- ✅ **Log a payment** (mock — or using testnet Celo for crypto learning).
- 📊 **See insights** on the services and earnings.

It's like a digital "yellow pages" + payment journal built for township entrepreneurs — by learners who want to uplift others through tech.

---

## 👨‍💻 Who built this?

We’re four learners, each bringing our unique background and goals:

- **Itumeleng Zulu** – Designed the front-end with a focus on accessibility. Believes the internet can help level the playing field and protect constitutional rights.
- **<PERSON><PERSON><PERSON>** – Handled the backend and cloud logic using Firebase. Loves building smart, scalable solutions for real-world use.
- **<PERSON><PERSON><PERSON>** – Created a simple dashboard to show service trends. He’s bridging finance and data for the next generation of fintech.
- **Jason Arnold** – Built a basic Celo smart contract simulation to show how township service providers could one day receive crypto payments directly.

---

## 🛠️ Tech Stack

| Part | Tools Used |
|------|------------|
| Frontend | HTML + JS (or React), Tailwind CSS |
| Backend | Firebase Firestore, Firebase Functions |
| Auth | Firebase Authentication |
| Hosting | Firebase Hosting or Vercel |
| Data | Firestore → CSV / Simple dashboard |
| Blockchain Sim | **Celo + Remix IDE + Alfajores Testnet** |

---

## 📦 How It Works

1. Sign up with email (Firebase Auth).
2. Add your service (title, category, price, location, contact).
3. Users browse available services and contact providers via WhatsApp/SMS.
4. Providers mark services as paid (manual/mocked), or log **testnet Celo transfers** using a simple smart contract deployed on Alfajores.
5. Users view a simple dashboard showing stats like total services listed, mock earnings, and categories.

---

## 🌍 Why Celo?

- ✅ Easy to use, fast, low-cost — perfect for mobile-first African use cases.
- 🌐 Celo’s mission aligns with ours: **financial inclusion and digital empowerment**.
- 🧪 We used [Remix IDE](https://remix.ethereum.org/) and Celo’s [Alfajores testnet](https://docs.celo.org/getting-started/alfajores-testnet-faucet) to simulate basic peer-to-peer payments.

---

## 🌱 Why we built this

We wanted to:

- Solve a **real problem**: lack of tools for small service providers in SA townships.
- Learn by building a **complete, working MVP** we can demo to mentors and communities.
- Show that beginners can dream big and **start small, smart, and together**.

---

## 🚀 What’s next?

- Add real geolocation to find services near you.
- Allow USSD/SMS sign-up for people without smartphones.
- Add real mobile Celo payments using Valora wallets.
- Collaborate with township entrepreneurs to test and improve it.

---

## 🙏 Special Thanks

To WeThinkCode_, the Celo community, and all mentors who’ve helped us learn and grow.

---

**Built with ❤️ in Johannesburg, South Africa**  
Empowering communities, one service at a time.

# 🇿🇦 MzansiServices – A Community-Powered Service Directory & P2P Tracker

Welcome to **MzansiServices**, a beginner-friendly tech project built by four passionate learners from WeThinkCode_ JHB campus. Our mission is simple: **make it easier for people in townships and under-resourced areas to connect, offer services, and track their income – even without a fancy app or expensive tools**.

---

## 💡 What's the idea?

MzansiServices is a simple website where anyone can:

- 📢 **List a service** they offer (like braiding, tutoring, laptop repairs).
- 🔎 **View local services** available nearby.
- 📞 **Contact a service provider** (via WhatsApp or SMS).
- ✅ **Log a payment** (mock — or using testnet Celo for crypto learning).
- 📊 **See insights** on the services and earnings.

It's like a digital "yellow pages" + payment journal built for township entrepreneurs — by learners who want to uplift others through tech.

---

## 👨‍💻 Who built this?

We’re four learners, each bringing our unique background and goals:

- **Itumeleng Zulu** – Designed the front-end with a focus on accessibility. Believes the internet can help level the playing field and protect constitutional rights.
- **Riyaaz Mahamed** – Handled the backend and cloud logic using Firebase. Loves building smart, scalable solutions for real-world use.
- **Tumiso Dinkoanyane** – Created a simple dashboard to show service trends. He’s bridging finance and data for the next generation of fintech.
- **Jason Arnold** – Built a basic Celo smart contract simulation to show how township service providers could one day receive crypto payments directly.

---

## 🛠️ Tech Stack

| Part | Tools Used |
|------|------------|
| Frontend | HTML + JS (or React), Tailwind CSS |
| Backend | Firebase Firestore, Firebase Functions |
| Auth | Firebase Authentication |
| Hosting | Firebase Hosting or Vercel |
| Data | Firestore → CSV / Simple dashboard |
| Blockchain Sim | **Celo + Remix IDE + Alfajores Testnet** |

---

## 📦 How It Works

1. Sign up with email (Firebase Auth).
2. Add your service (title, category, price, location, contact).
3. Users browse available services and contact providers via WhatsApp/SMS.
4. Providers mark services as paid (manual/mocked), or log **testnet Celo transfers** using a simple smart contract deployed on Alfajores.
5. Users view a simple dashboard showing stats like total services listed, mock earnings, and categories.

---

## 🌍 Why Celo?

- ✅ Easy to use, fast, low-cost — perfect for mobile-first African use cases.
- 🌐 Celo’s mission aligns with ours: **financial inclusion and digital empowerment**.
- 🧪 We used [Remix IDE](https://remix.ethereum.org/) and Celo’s [Alfajores testnet](https://docs.celo.org/getting-started/alfajores-testnet-faucet) to simulate basic peer-to-peer payments.

---

## 🌱 Why we built this

We wanted to:

- Solve a **real problem**: lack of tools for small service providers in SA townships.
- Learn by building a **complete, working MVP** we can demo to mentors and communities.
- Show that beginners can dream big and **start small, smart, and together**.

---

## 🚀 What’s next?

- Add real geolocation to find services near you.
- Allow USSD/SMS sign-up for people without smartphones.
- Add real mobile Celo payments using Valora wallets.
- Collaborate with township entrepreneurs to test and improve it.

---

## 🙏 Special Thanks

To WeThinkCode_, the Celo community, and all mentors who’ve helped us learn and grow.

---

**Built with ❤️ in Johannesburg, South Africa**  
Empowering communities, one service at a time.
